#!/usr/bin/env python3
"""
Script to fix training data issues:
1. Fix invalid span positions that exceed text length
2. Handle multi-element entity formats (4+ elements)
3. Create additional data for undersampled entities
"""

import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Tuple
from collections import Counter
import random

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s | %(message)s')
logger = logging.getLogger(__name__)

class TrainingDataFixer:
    def __init__(self):
        self.data_dir = Path("data/training")
        self.fixed_count = 0
        self.invalid_count = 0
        self.entity_stats = Counter()
        
    def fix_entity_format(self, entity: List) -> Tuple[int, int, str]:
        """
        Fix entity format to extract start, end, label from various formats.
        
        Args:
            entity: Entity annotation in various formats
            
        Returns:
            Tuple of (start, end, label)
        """
        if len(entity) == 3:
            return entity[0], entity[1], entity[2]
        elif len(entity) == 4:
            # Handle 4-element format
            if isinstance(entity[2], str):
                # Format: [start, end, label, extra_info] - use first 3 elements
                return entity[0], entity[1], entity[2]
            else:
                # Format: [start, extra_start, end, label] - use start, end (skip middle), label
                return entity[0], entity[2], entity[3]
        else:
            # For 5+ elements, take first, last-1, and last as start, end, label
            return entity[0], entity[-2], entity[-1]
    
    def fix_invalid_spans(self, text: str, entities: List[List]) -> List[List]:
        """
        Fix invalid span positions and formats.
        
        Args:
            text: The text content
            entities: List of entity annotations
            
        Returns:
            List of fixed entity annotations
        """
        fixed_entities = []
        text_length = len(text)
        
        for entity in entities:
            try:
                start, end, label = self.fix_entity_format(entity)
                
                # Validate and fix span positions
                if start < 0:
                    start = 0
                    self.fixed_count += 1
                    
                if end > text_length:
                    end = text_length
                    self.fixed_count += 1
                    
                if start >= end:
                    # Try to find a reasonable end position
                    words = text[start:].split()
                    if words:
                        # Take the first word after start position
                        end = min(start + len(words[0]), text_length)
                    else:
                        # Skip this entity if we can't fix it
                        self.invalid_count += 1
                        continue
                    self.fixed_count += 1
                
                # Ensure the span contains meaningful text
                span_text = text[start:end].strip()
                if span_text:
                    fixed_entities.append([start, end, label])
                    self.entity_stats[label] += 1
                else:
                    self.invalid_count += 1
                    
            except (IndexError, ValueError, TypeError) as e:
                logger.warning(f"Could not fix entity {entity}: {e}")
                self.invalid_count += 1
                continue
                
        return fixed_entities
    
    def process_file(self, input_path: Path, output_path: Path) -> Dict[str, int]:
        """
        Process a training data file and fix issues.
        
        Args:
            input_path: Path to input JSONL file
            output_path: Path to output fixed JSONL file
            
        Returns:
            Dictionary with processing statistics
        """
        logger.info(f"Processing {input_path}")
        
        processed_lines = 0
        valid_lines = 0
        
        with open(input_path, 'r', encoding='utf-8') as infile, \
             open(output_path, 'w', encoding='utf-8') as outfile:
            
            for line_num, line in enumerate(infile, 1):
                try:
                    data = json.loads(line.strip())
                    text = data["text"]
                    entities = data["entities"]
                    
                    # Fix entities
                    fixed_entities = self.fix_invalid_spans(text, entities)
                    
                    # Write fixed data
                    fixed_data = {
                        "text": text,
                        "entities": fixed_entities
                    }
                    
                    outfile.write(json.dumps(fixed_data, ensure_ascii=False) + '\n')
                    valid_lines += 1
                    
                except json.JSONDecodeError as e:
                    logger.error(f"JSON decode error at line {line_num}: {e}")
                except KeyError as e:
                    logger.error(f"Missing required key {e} at line {line_num}")
                except Exception as e:
                    logger.error(f"Error processing line {line_num}: {e}")
                
                processed_lines += 1
                
                if processed_lines % 1000 == 0:
                    logger.info(f"Processed {processed_lines} lines...")
        
        return {
            "processed_lines": processed_lines,
            "valid_lines": valid_lines,
            "fixed_spans": self.fixed_count,
            "invalid_entities": self.invalid_count
        }
    
    def generate_additional_data(self, entity_type: str, target_count: int = 100) -> List[Dict[str, Any]]:
        """
        Generate additional training data for undersampled entity types.
        
        Args:
            entity_type: The entity type to generate data for
            target_count: Number of additional samples to generate
            
        Returns:
            List of generated training samples
        """
        templates = {
            "DEPARTMENT": [
                "I work in the {entity} department.",
                "The {entity} team is handling this request.",
                "Please contact the {entity} department for assistance.",
                "My manager is from the {entity} division.",
                "The {entity} department has new policies."
            ],
            "EMPLOYEE_NAME": [
                "My name is {entity}.",
                "Please contact {entity} for more information.",
                "{entity} is my supervisor.",
                "I am {entity} from the HR department.",
                "{entity} submitted the leave request."
            ],
            "DOCUMENT_TYPE": [
                "I need my {entity} document.",
                "Please provide the {entity} form.",
                "The {entity} is required for processing.",
                "I want to download my {entity}.",
                "Where can I find the {entity}?"
            ],
            "HR_PROCESS": [
                "I need help with {entity}.",
                "The {entity} process is confusing.",
                "How do I complete the {entity}?",
                "My {entity} was rejected.",
                "I want to start the {entity} procedure."
            ],
            "POLICY_NAME": [
                "What is the {entity} policy?",
                "I need information about {entity}.",
                "The {entity} has been updated.",
                "Please explain the {entity} rules.",
                "I want to review the {entity}."
            ],
            "HR_EVENT": [
                "I attended the {entity} yesterday.",
                "When is the next {entity} scheduled?",
                "The {entity} was very informative.",
                "I missed the {entity} due to illness.",
                "Please register me for the {entity}.",
                "The {entity} is mandatory for all employees.",
                "I need the materials from the {entity}."
            ],
            "salary_type": [
                "My {entity} needs to be updated.",
                "What is my current {entity}?",
                "I want to change my {entity}.",
                "The {entity} calculation seems incorrect.",
                "Please explain the {entity} structure.",
                "My {entity} is different from last month.",
                "I need clarification on my {entity}."
            ]
        }
        
        sample_values = {
            "DEPARTMENT": ["HR", "Finance", "IT", "Marketing", "Sales", "Operations", "Legal", "Engineering"],
            "EMPLOYEE_NAME": ["John Smith", "Sarah Johnson", "Mike Davis", "Lisa Wilson", "David Brown", "Emma Taylor"],
            "DOCUMENT_TYPE": ["payslip", "offer letter", "experience certificate", "salary certificate", "tax form"],
            "HR_PROCESS": ["onboarding", "performance review", "leave application", "resignation", "promotion"],
            "POLICY_NAME": ["leave policy", "remote work policy", "expense policy", "code of conduct", "travel policy"],
            "HR_EVENT": ["training session", "team meeting", "performance review", "orientation", "workshop", "seminar", "town hall", "team building"],
            "salary_type": ["basic salary", "gross salary", "net salary", "hourly rate", "annual salary", "monthly salary", "fixed salary", "variable pay"]
        }
        
        if entity_type not in templates:
            logger.warning(f"No templates available for entity type: {entity_type}")
            return []
        
        generated_data = []
        entity_templates = templates[entity_type]
        entity_values = sample_values[entity_type]
        
        for _ in range(target_count):
            template = random.choice(entity_templates)
            value = random.choice(entity_values)
            text = template.format(entity=value)
            
            # Find the entity position in the text
            start = text.find(value)
            end = start + len(value)
            
            generated_data.append({
                "text": text,
                "entities": [[start, end, entity_type]]
            })
        
        return generated_data
    
    def run(self):
        """Run the complete data fixing process."""
        logger.info("Starting training data fixing process...")
        
        # Files to process
        files_to_process = [
            ("entity_training_data.jsonl", "entity_training_data_fixed.jsonl"),
            ("entity_validation_data.jsonl", "entity_validation_data_fixed.jsonl")
        ]
        
        total_stats = {
            "processed_lines": 0,
            "valid_lines": 0,
            "fixed_spans": 0,
            "invalid_entities": 0
        }
        
        # Process each file
        for input_file, output_file in files_to_process:
            input_path = self.data_dir / input_file
            output_path = self.data_dir / output_file
            
            if input_path.exists():
                stats = self.process_file(input_path, output_path)
                
                # Update total stats
                for key in total_stats:
                    total_stats[key] += stats[key]
                
                logger.info(f"Fixed {input_file}: {stats}")
            else:
                logger.warning(f"File not found: {input_path}")
        
        # Print entity statistics
        logger.info("Entity type distribution:")
        for entity_type, count in self.entity_stats.most_common():
            logger.info(f"  {entity_type}: {count}")
        
        # Identify undersampled entities (less than 50 occurrences)
        undersampled = [entity for entity, count in self.entity_stats.items() if count < 50]
        
        if undersampled:
            logger.info(f"Undersampled entities: {undersampled}")
            
            # Generate additional data for undersampled entities
            additional_data = []
            for entity_type in undersampled:
                if entity_type in ["DEPARTMENT", "EMPLOYEE_NAME", "DOCUMENT_TYPE", "HR_PROCESS", "POLICY_NAME", "HR_EVENT", "salary_type"]:
                    # Generate more samples for very undersampled entities
                    target_samples = 100 if self.entity_stats[entity_type] < 10 else 50
                    new_samples = self.generate_additional_data(entity_type, target_samples)
                    additional_data.extend(new_samples)
                    logger.info(f"Generated {len(new_samples)} additional samples for {entity_type}")
            
            # Save additional data
            if additional_data:
                additional_file = self.data_dir / "entity_additional_data.jsonl"
                with open(additional_file, 'w', encoding='utf-8') as f:
                    for sample in additional_data:
                        f.write(json.dumps(sample, ensure_ascii=False) + '\n')
                logger.info(f"Saved {len(additional_data)} additional samples to {additional_file}")
        
        logger.info("Training data fixing completed!")
        logger.info(f"Total statistics: {total_stats}")

if __name__ == "__main__":
    fixer = TrainingDataFixer()
    fixer.run()
