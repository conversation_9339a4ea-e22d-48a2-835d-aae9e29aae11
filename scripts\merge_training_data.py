#!/usr/bin/env python3
"""
Script to merge fixed training data with additional generated data
and create final clean training files.
"""

import json
import logging
from pathlib import Path
from typing import List, Dict, Any
from collections import Counter
import random

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s | %(message)s')
logger = logging.getLogger(__name__)

class TrainingDataMerger:
    def __init__(self):
        self.data_dir = Path("data/training")
        
    def merge_files(self, base_file: str, additional_file: str, output_file: str):
        """
        Merge base training data with additional generated data.
        
        Args:
            base_file: Base training data file (fixed)
            additional_file: Additional generated data file
            output_file: Output merged file
        """
        base_path = self.data_dir / base_file
        additional_path = self.data_dir / additional_file
        output_path = self.data_dir / output_file
        
        logger.info(f"Merging {base_file} with {additional_file} -> {output_file}")
        
        # Read base data
        base_data = []
        if base_path.exists():
            with open(base_path, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        base_data.append(data)
                    except json.JSONDecodeError:
                        continue
        
        # Read additional data
        additional_data = []
        if additional_path.exists():
            with open(additional_path, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        additional_data.append(data)
                    except json.JSONDecodeError:
                        continue
        
        # Combine and shuffle
        combined_data = base_data + additional_data
        random.shuffle(combined_data)
        
        # Write merged data
        with open(output_path, 'w', encoding='utf-8') as f:
            for item in combined_data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        logger.info(f"Merged {len(base_data)} base + {len(additional_data)} additional = {len(combined_data)} total samples")
        return len(combined_data)
    
    def analyze_entity_distribution(self, file_path: str) -> Dict[str, int]:
        """
        Analyze entity type distribution in a training file.
        
        Args:
            file_path: Path to the training file
            
        Returns:
            Dictionary with entity type counts
        """
        entity_counts = Counter()
        
        with open(self.data_dir / file_path, 'r', encoding='utf-8') as f:
            for line in f:
                try:
                    data = json.loads(line.strip())
                    entities = data.get("entities", [])
                    
                    for entity in entities:
                        if len(entity) >= 3:
                            label = entity[-1]  # Last element is always the label
                            entity_counts[label] += 1
                            
                except json.JSONDecodeError:
                    continue
        
        return dict(entity_counts)
    
    def create_validation_split(self, training_file: str, validation_file: str, split_ratio: float = 0.1):
        """
        Create a validation split from training data.
        
        Args:
            training_file: Input training file
            validation_file: Output validation file
            split_ratio: Ratio of data to use for validation
        """
        training_path = self.data_dir / training_file
        validation_path = self.data_dir / validation_file
        
        # Read all training data
        all_data = []
        with open(training_path, 'r', encoding='utf-8') as f:
            for line in f:
                try:
                    data = json.loads(line.strip())
                    all_data.append(data)
                except json.JSONDecodeError:
                    continue
        
        # Shuffle and split
        random.shuffle(all_data)
        split_index = int(len(all_data) * split_ratio)
        
        validation_data = all_data[:split_index]
        training_data = all_data[split_index:]
        
        # Write validation data
        with open(validation_path, 'w', encoding='utf-8') as f:
            for item in validation_data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        # Write updated training data
        with open(training_path, 'w', encoding='utf-8') as f:
            for item in training_data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        logger.info(f"Created validation split: {len(training_data)} training, {len(validation_data)} validation")
        return len(training_data), len(validation_data)
    
    def run(self):
        """Run the complete merging process."""
        logger.info("Starting training data merging process...")
        
        # Set random seed for reproducibility
        random.seed(42)
        
        # Step 1: Merge fixed training data with additional data
        total_samples = self.merge_files(
            "entity_training_data_fixed.jsonl",
            "entity_additional_data.jsonl", 
            "entity_training_data_final.jsonl"
        )
        
        # Step 2: Create validation split from the merged data
        train_count, val_count = self.create_validation_split(
            "entity_training_data_final.jsonl",
            "entity_validation_data_final.jsonl",
            split_ratio=0.1
        )
        
        # Step 3: Analyze final entity distribution
        logger.info("Final entity distribution in training data:")
        train_distribution = self.analyze_entity_distribution("entity_training_data_final.jsonl")
        for entity_type, count in sorted(train_distribution.items(), key=lambda x: x[1], reverse=True):
            logger.info(f"  {entity_type}: {count}")
        
        logger.info("Final entity distribution in validation data:")
        val_distribution = self.analyze_entity_distribution("entity_validation_data_final.jsonl")
        for entity_type, count in sorted(val_distribution.items(), key=lambda x: x[1], reverse=True):
            logger.info(f"  {entity_type}: {count}")
        
        # Step 4: Create backup of original files and replace them
        logger.info("Creating backups and updating main files...")
        
        # Backup original files
        original_train = self.data_dir / "entity_training_data.jsonl"
        original_val = self.data_dir / "entity_validation_data.jsonl"
        backup_train = self.data_dir / "entity_training_data_backup.jsonl"
        backup_val = self.data_dir / "entity_validation_data_backup.jsonl"
        
        if original_train.exists():
            original_train.rename(backup_train)
            logger.info(f"Backed up original training data to {backup_train}")
        
        if original_val.exists():
            original_val.rename(backup_val)
            logger.info(f"Backed up original validation data to {backup_val}")
        
        # Copy final files to main locations
        final_train = self.data_dir / "entity_training_data_final.jsonl"
        final_val = self.data_dir / "entity_validation_data_final.jsonl"
        
        if final_train.exists():
            with open(final_train, 'r', encoding='utf-8') as src, \
                 open(original_train, 'w', encoding='utf-8') as dst:
                dst.write(src.read())
            logger.info(f"Updated main training file with {train_count} samples")
        
        if final_val.exists():
            with open(final_val, 'r', encoding='utf-8') as src, \
                 open(original_val, 'w', encoding='utf-8') as dst:
                dst.write(src.read())
            logger.info(f"Updated main validation file with {val_count} samples")
        
        logger.info("Training data merging completed successfully!")
        logger.info(f"Summary:")
        logger.info(f"  - Total training samples: {train_count}")
        logger.info(f"  - Total validation samples: {val_count}")
        logger.info(f"  - Total entity types: {len(train_distribution)}")
        logger.info(f"  - Fixed span errors and invalid formats")
        logger.info(f"  - Added data for undersampled entities")

if __name__ == "__main__":
    merger = TrainingDataMerger()
    merger.run()
