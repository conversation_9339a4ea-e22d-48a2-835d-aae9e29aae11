
(C:\Generative AI Projects\Multi-Model RAG Chatbot-react\venv) C:\Generative AI Projects\Multi-Model RAG Chatbot-react>python -m src.ner.train_entity_extractor
2025-08-01 16:37:01,013 | __main__:35 | INFO     | __init__ | PID:15596 | TID:MainThread | Using device: cpu
2025-08-01 16:37:01,013 | __main__:49 | INFO     | load_training_data | PID:15596 | TID:MainThread | Loading training data from: data/training/entity_training_data.jsonl
2025-08-01 16:37:01,336 | __main__:101 | INFO     | load_training_data | PID:15596 | TID:MainThread | Loaded 10728 training samples
2025-08-01 16:37:01,336 | __main__:143 | INFO     | train | PID:15596 | TID:MainThread | Loaded 10728 training samples
2025-08-01 16:37:01,336 | __main__:49 | INFO     | load_training_data | PID:15596 | TID:MainThread | Loading training data from: data/training/entity_validation_data.jsonl
2025-08-01 16:37:01,393 | __main__:101 | INFO     | load_training_data | PID:15596 | TID:MainThread | Loaded 1191 training samples
2025-08-01 16:37:01,393 | __main__:149 | INFO     | train | PID:15596 | TID:MainThread | Loaded 1191 validation samples
2025-08-01 16:37:01,425 | __main__:116 | INFO     | get_unique_labels | PID:15596 | TID:MainThread | Found 24 unique entity labels: ['BENEFIT_TYPE', 'DATE', 'DEPARTMENT', 'DOCUMENT_TYPE', 'EMPLOYEE_NAME', 'HR_EVENT', 'HR_PROCESS', 'LEAVE_BALANCE', 'LEAVE_TYPE', 'MONTH', 'O', 'POLICY_NAME', 'YEAR', 'amount', 'download_payslip', 'expense_type', 'intent_routing', 'issue_type', 'ner', 'payslip_context', 'salary_component', 'salary_type', 'summarization', 'text_extraction']  
2025-08-01 16:37:01,836 | __main__:164 | INFO     | train | PID:15596 | TID:MainThread | Initializing SpanMarker model from microsoft/deberta-v3-base
C:\Generative AI Projects\Multi-Model RAG Chatbot-react\venv\lib\site-packages\transformers\convert_slow_tokenizer.py:561: UserWarning: The sentencepiece tokenizer that you are converting to a fast tokenizer uses the byte fallback option which is not implemented in the fast tokenizers. In practice this means that the fast version of the tokenizer can produce unknown tokens whereas the sentencepiece version would have converted these unknown tokens into a sequence of byte tokens matching the original piece of text.
  warnings.warn(
C:\Generative AI Projects\Multi-Model RAG Chatbot-react\venv\lib\site-packages\transformers\training_args.py:1594: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
2025-08-01 16:37:11,401 | span_marker.label_normalizer:99 | INFO     | __init__ | PID:15596 | TID:MainThread | No labeling scheme detected: all label IDs belong to individual entity classes.
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
C:\Generative AI Projects\Multi-Model RAG Chatbot-react\venv\lib\site-packages\span_marker\trainer.py:140: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  super().__init__(
2025-08-01 16:37:14,880 | __main__:212 | INFO     | train | PID:15596 | TID:MainThread | Starting training...
Trainer.tokenizer is now deprecated. You should use Trainer.processing_class instead.
2025-08-01 16:37:15,177 | __main__:294 | ERROR    | main | PID:15596 | TID:MainThread | Training failed: The train dataset must contain a 'tokens' column.

❌ Training failed: The train dataset must contain a 'tokens' column.