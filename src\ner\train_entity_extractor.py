#!/usr/bin/env python3
"""
Training script for Entity Extractor using SpanMarker.
"""

import json
import logging
import argparse
import os
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

# Disable wandb completely
os.environ["WANDB_DISABLED"] = "true"
os.environ["WANDB_MODE"] = "disabled"

import torch
from span_marker import SpanMarkerModel, <PERSON>er as SpanMarkerTrainer
from transformers import TrainingArguments
from datasets import Dataset


def get_gpu_memory_gb() -> float:
    """Get available GPU memory in GB."""
    if torch.cuda.is_available():
        return torch.cuda.get_device_properties(0).total_memory / (1024**3)
    else:
        logger.warning("CUDA not available, using CPU")
        return 0


def get_dynamic_batch_size(gpu_memory_gb: float, override_batch_size: Optional[int] = None) -> int:
    """
    Determine optimal batch size based on available GPU memory.
    
    Args:
        gpu_memory_gb: Available GPU memory in GB
        override_batch_size: Optional CLI override
    
    Returns:
        Optimal batch size
    """
    if override_batch_size is not None:
        logger.info(f"Using CLI override batch size: {override_batch_size}")
        return override_batch_size
    
    if gpu_memory_gb >= 12:
        batch_size = 32
        logger.info(f"GPU memory: {gpu_memory_gb:.1f}GB >= 12GB, using batch_size=32")
    elif gpu_memory_gb >= 8:
        batch_size = 16
        logger.info(f"GPU memory: {gpu_memory_gb:.1f}GB (8-12GB), using batch_size=16")
    else:
        batch_size = 8
        logger.info(f"GPU memory: {gpu_memory_gb:.1f}GB < 8GB, using batch_size=8")
    
    return batch_size

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(name)s:%(lineno)d | %(levelname)-8s | %(funcName)s | PID:%(process)d | TID:%(threadName)s | %(message)s'
)
logger = logging.getLogger(__name__)

class EntityExtractorTrainer:
    """Trainer for SpanMarker-based entity extraction."""
    
    def __init__(self, base_model: str = "bert-base-uncased"):
        """
        Initialize the trainer.
        
        Args:
            base_model: Base model to use for training.
        """
        self.base_model = base_model
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"Using device: {self.device}")
    
    def load_training_data(self, data_path: str) -> List[Dict[str, Any]]:
        """
        Load and convert training data to SpanMarker format.

        Args:
            data_path: Path to JSONL training data file.

        Returns:
            List of training samples in SpanMarker format with 'tokens' and 'ner_tags' columns.
        """
        training_data = []

        logger.info(f"Loading training data from: {data_path}")

        with open(data_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())
                    text = data["text"]
                    entities = data["entities"]

                    # Convert text to tokens and entities to BIO tags
                    tokens, ner_tags = self._convert_to_bio_format(text, entities, line_num)

                    if tokens and ner_tags:
                        training_data.append({
                            "tokens": tokens,
                            "ner_tags": ner_tags
                        })

                except json.JSONDecodeError as e:
                    logger.error(f"JSON decode error at line {line_num}: {e}")
                except KeyError as e:
                    logger.error(f"Missing required key {e} at line {line_num}")
                except Exception as e:
                    logger.error(f"Error processing line {line_num}: {e}")

        logger.info(f"Loaded {len(training_data)} training samples")
        return training_data

    def _convert_to_bio_format(self, text: str, entities: List[List], line_num: int) -> Tuple[List[str], List[str]]:
        """
        Convert text and entities to BIO format.

        Args:
            text: Input text
            entities: List of entities in format [start, end, label]
            line_num: Line number for error reporting

        Returns:
            Tuple of (tokens, ner_tags) in BIO format
        """
        # Simple whitespace tokenization
        tokens = text.split()
        ner_tags = ["O"] * len(tokens)

        # Calculate character positions for each token
        token_positions = []
        char_pos = 0
        for token in tokens:
            # Find the start position of this token in the text
            while char_pos < len(text) and text[char_pos].isspace():
                char_pos += 1
            start_pos = char_pos
            end_pos = start_pos + len(token)
            token_positions.append((start_pos, end_pos))
            char_pos = end_pos

        # Process entities and assign BIO tags
        for entity in entities:
            if len(entity) >= 3:
                # Handle different entity formats flexibly
                if len(entity) == 3:
                    start, end, label = entity
                elif len(entity) == 4:
                    # Handle 4-element format: [start, end, extra_info, label] or [start, extra_start, end, label]
                    if isinstance(entity[2], str):
                        # Format: [start, end, label, extra_info] - use first 3 elements
                        start, end, label = entity[0], entity[1], entity[2]
                    else:
                        # Format: [start, extra_start, end, label] - use start, end (skip middle), label
                        start, end, label = entity[0], entity[2], entity[3]
                else:
                    # For 5+ elements, take first, last-1, and last as start, end, label
                    start, end, label = entity[0], entity[-2], entity[-1]

                # Validate span
                if not (0 <= start < end <= len(text)):
                    logger.warning(f"Invalid span [{start}, {end}] for text length {len(text)} at line {line_num}")
                    continue

                # Find tokens that overlap with this entity
                entity_tokens = []
                for i, (token_start, token_end) in enumerate(token_positions):
                    # Check if token overlaps with entity span
                    if (token_start < end and token_end > start):
                        entity_tokens.append(i)

                # Assign BIO tags
                for i, token_idx in enumerate(entity_tokens):
                    if i == 0:
                        ner_tags[token_idx] = f"B-{label}"
                    else:
                        ner_tags[token_idx] = f"I-{label}"
            else:
                logger.warning(f"Skipping invalid entity format (need at least 3 elements): {entity} at line {line_num}")

        return tokens, ner_tags
    
    def get_unique_labels(self, datasets: List[List[Dict[str, Any]]]) -> List[str]:
        """Extract unique labels from datasets with BIO format."""
        all_labels = set()
        for dataset in datasets:
            for item in dataset:
                for tag in item["ner_tags"]:
                    all_labels.add(tag)

        labels = sorted(list(all_labels))
        logger.info(f"Found {len(labels)} unique entity labels: {labels}")
        return labels
    
    def train(self, 
              training_data_path: str,
              validation_data_path: Optional[str] = None,
              output_path: str = "data/models/ner_model",
              num_epochs: int = 5,
              batch_size: Optional[int] = None,
              learning_rate: float = 5e-5,
              max_length: int = 1024,
              entity_max_length: int = 12):
        """
        Train the SpanMarker model.
        
        Args:
            training_data_path: Path to training data JSONL file.
            validation_data_path: Path to validation data JSONL file.
            output_path: Path to save the trained model.
            num_epochs: Number of training epochs.
            batch_size: Training batch size.
            learning_rate: Learning rate for training.
            max_length: Maximum sequence length.
            entity_max_length: Maximum entity length in words.
        """
        # Load training data
        train_data = self.load_training_data(training_data_path)
        logger.info(f"Loaded {len(train_data)} training samples")

        # Load validation data if available
        eval_data = None
        if validation_data_path and Path(validation_data_path).exists():
            eval_data = self.load_training_data(validation_data_path)
            logger.info(f"Loaded {len(eval_data)} validation samples")
        else:
            logger.warning("No validation data found. Training without validation.")

        # Get unique labels
        datasets = [train_data]
        if eval_data:
            datasets.append(eval_data)
        labels = self.get_unique_labels(datasets)

        # Convert to Hugging Face Dataset objects
        train_dataset = Dataset.from_list(train_data)
        eval_dataset = Dataset.from_list(eval_data) if eval_data else None
        
        # Determine optimal batch size based on GPU memory
        gpu_memory_gb = get_gpu_memory_gb()
        optimal_batch_size = get_dynamic_batch_size(gpu_memory_gb, batch_size)
        logger.info(f"Using batch size: {optimal_batch_size}")
        
        # Create model
        logger.info(f"Initializing SpanMarker model from {self.base_model}")
        model = SpanMarkerModel.from_pretrained(
            self.base_model,
            labels=labels,
            model_max_length=max_length,
            entity_max_length=entity_max_length,
            mean_resizing=False  # Disable mean resizing warning
        )
        
        # Move to device
        if self.device == "cuda":
            model = model.cuda()
            logger.info("Model moved to GPU")
        
        # Create output directory
        output_dir = Path(output_path)
        output_dir.mkdir(parents=True, exist_ok=True)

        # Training arguments
        args = TrainingArguments(
            output_dir=str(output_dir),
            learning_rate=learning_rate,
            per_device_train_batch_size=optimal_batch_size,
            per_device_eval_batch_size=optimal_batch_size,
            num_train_epochs=num_epochs,
            warmup_ratio=0.1,
            bf16=False,
            fp16=torch.cuda.is_available(),
            gradient_checkpointing=False,
            dataloader_num_workers=0,
            save_strategy="epoch",
            eval_strategy="epoch" if eval_dataset else "no",
            logging_steps=50,
            save_total_limit=2,
            load_best_model_at_end=True if eval_dataset else False,
            metric_for_best_model="eval_f1" if eval_dataset else None,
            greater_is_better=True,
            report_to="none",  # Disable wandb and other logging
            remove_unused_columns=False,
        )

        # Create trainer
        trainer = SpanMarkerTrainer(
            model=model,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            args=args
        )
        
        # Train the model
        logger.info("Starting training...")
        trainer.train()
        
        # Evaluate the model
        if eval_dataset:
            logger.info("Evaluating model...")
            eval_results = trainer.evaluate()
            logger.info(f"Evaluation results: {eval_results}")
        
        # Save the model
        logger.info(f"Saving model to {output_dir}")
        trainer.save_model(str(output_dir))
        
        # Save entity types for reference
        entity_types_path = output_dir / "entity_types.json"
        with open(entity_types_path, 'w', encoding='utf-8') as f:
            json.dump({
                "entity_types": labels,
                "encoder": self.base_model,
                "total_entity_types": len(labels)
            }, f, indent=2)
        
        logger.info(f"Training completed successfully. Model saved to: {output_dir}")
        
        return {
            "model_path": str(output_dir),
            "entity_types": labels,
            "training_samples": len(train_data),
            "validation_samples": len(eval_data) if eval_data else 0,
            "epochs": num_epochs
        }


def main():
    """Main training function."""
    parser = argparse.ArgumentParser(description="Train Entity Extractor")
    parser.add_argument("--training-data", type=str, 
                       default="data/training/entity_training_data.jsonl",
                       help="Path to training data JSONL file")
    parser.add_argument("--validation-data", type=str,
                       default="data/training/entity_validation_data.jsonl", 
                       help="Path to validation data JSONL file")
    parser.add_argument("--output-path", type=str,
                       default="data/models/ner_model",
                       help="Path to save the trained model")
    parser.add_argument("--epochs", type=int, default=5,
                       help="Number of training epochs")
    parser.add_argument("--batch-size", type=int, default=8,
                       help="Training batch size")
    parser.add_argument("--learning-rate", type=float, default=5e-5,
                       help="Learning rate")
    parser.add_argument("--base-model", type=str,
                       default="bert-base-uncased",
                       help="Base model to use")
    
    args = parser.parse_args()
    
    # Initialize trainer
    trainer = EntityExtractorTrainer(base_model=args.base_model)
    
    # Start training
    try:
        result = trainer.train(
            training_data_path=args.training_data,
            validation_data_path=args.validation_data,
            output_path=args.output_path,
            num_epochs=args.epochs,
            batch_size=args.batch_size,
            learning_rate=args.learning_rate
        )
        
        print("\n" + "="*50)
        print("🎉 TRAINING COMPLETED SUCCESSFULLY!")
        print("="*50)
        print(f"📁 Model saved to: {result['model_path']}")
        print(f"🏷️  Entity types: {len(result['entity_types'])}")
        print(f"📊 Training samples: {result['training_samples']}")
        print(f"📊 Validation samples: {result['validation_samples']}")
        print(f"🔄 Epochs: {result['epochs']}")
        print("="*50)
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        print(f"\n❌ Training failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
